################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
BSP/Src/%.o: ../BSP/Src/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"E:/TI/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/Desktop/TI_CAR-0/TI_CAR1/TI_CAR1/BSP/Inc" -I"C:/Users/<USER>/Desktop/TI_CAR-0/TI_CAR1/TI_CAR1" -I"C:/Users/<USER>/Desktop/TI_CAR-0/TI_CAR1/TI_CAR1/DMP" -I"C:/Users/<USER>/Desktop/TI_CAR-0/TI_CAR1/TI_CAR1/App/Inc" -I"C:/Users/<USER>/Desktop/TI_CAR-0/TI_CAR1/TI_CAR1/Debug" -I"C:/TI/mspm0_sdk_2_04_00_06/source/third_party/CMSIS/Core/Include" -I"C:/TI/mspm0_sdk_2_04_00_06/source" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"BSP/Src/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


