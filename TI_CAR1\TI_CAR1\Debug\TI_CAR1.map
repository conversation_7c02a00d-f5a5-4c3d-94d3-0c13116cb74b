******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Thu Jul 31 22:28:20 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005289


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00006110  00019ef0  R  X
  SRAM                  20200000   00008000  000006e1  0000791f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00006110   00006110    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005d60   00005d60    r-x .text
  00005e20    00005e20    00000290   00000290    r-- .rodata
  000060b0    000060b0    00000060   00000060    r-- .cinit
20200000    20200000    000004e2   00000000    rw-
  20200000    20200000    000003d3   00000000    rw- .bss
  202003d4    202003d4    0000010e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005d60     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    0000022c     MPU6050.o (.text.Read_Quad)
                  00001318    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001544    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001764    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001958    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001b38    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001d14    000001b0     Task.o (.text.Task_Start)
                  00001ec4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002056    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002058    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  000021e0    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00002368    00000170            : e_sqrt.c.obj (.text.sqrt)
                  000024d8    0000013c            : _printfi.c.obj (.text.fcvt)
                  00002614    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00002748    00000134     libc.a : qsort.c.obj (.text.qsort)
                  0000287c    00000128     Task_App.o (.text.Task_Tracker)
                  000029a4    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002ac4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002bd0    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002cd8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002ddc    000000f0     Motor.o (.text.Motor_SetDirc)
                  00002ecc    000000e4     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002fb0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003094    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003170    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000324c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003324    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000033fc    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  000034c0    000000b4     Task.o (.text.Task_Add)
                  00003574    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  0000361e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003620    000000a2                            : udivmoddi4.S.obj (.text)
                  000036c2    00000002     --HOLE-- [fill = 0]
                  000036c4    000000a0     Motor.o (.text.Motor_SetDuty)
                  00003764    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003804    00000094     Task_App.o (.text.Task_Init)
                  00003898    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  00003924    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000039b0    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00003a3c    00000084     Serial.o (.text.MyPrintf)
                  00003ac0    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00003b44    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003bc8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003c4a    00000002     --HOLE-- [fill = 0]
                  00003c4c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003cc8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003d3c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00003d40    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003db4    00000070     Motor.o (.text.Motor_Start)
                  00003e24    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00003e90    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003ef8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00003f5e    00000002     --HOLE-- [fill = 0]
                  00003f60    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00003fc4    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00004028    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000408a    00000002     --HOLE-- [fill = 0]
                  0000408c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  000040ee    00000002     --HOLE-- [fill = 0]
                  000040f0    00000060     Task_App.o (.text.Task_IdleFunction)
                  00004150    00000060     Task_App.o (.text.Task_Serial)
                  000041b0    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00004210    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  0000426e    00000002     --HOLE-- [fill = 0]
                  00004270    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000042cc    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00004328    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00004384    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000043dc    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00004434    00000058     Serial.o (.text.Serial_Init)
                  0000448c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000044e4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000453c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004592    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000045e4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00004634    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00004684    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000046d0    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000471c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004768    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000047b2    00000002     --HOLE-- [fill = 0]
                  000047b4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000047fe    00000002     --HOLE-- [fill = 0]
                  00004800    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004848    00000048     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00004890    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  000048d8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000491c    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  00004960    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  000049a2    00000002     --HOLE-- [fill = 0]
                  000049a4    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000049e6    00000002     --HOLE-- [fill = 0]
                  000049e8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004a28    00000040     Interrupt.o (.text.Interrupt_Init)
                  00004a68    00000040     Task_App.o (.text.Task_GraySensor)
                  00004aa8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004ae8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004b28    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004b68    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00004ba8    0000003e     Task.o (.text.Task_CMP)
                  00004be6    00000002     --HOLE-- [fill = 0]
                  00004be8    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004c24    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004c60    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00004c9c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004cd8    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00004d14    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004d50    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00004d8c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004dc8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00004e02    00000002     --HOLE-- [fill = 0]
                  00004e04    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00004e3e    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00004e76    00000002     --HOLE-- [fill = 0]
                  00004e78    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004eb0    00000038     ADC.o (.text.adc_getValue)
                  00004ee8    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004f1c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004f50    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00004f80    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00004fb0    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00004fe0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00005010    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005040    00000030            : vsnprintf.c.obj (.text._outs)
                  00005070    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000509c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  000050c8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  000050f4    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  0000511e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00005146    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000516e    00000002     --HOLE-- [fill = 0]
                  00005170    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00005198    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000051c0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000051e8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00005210    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00005238    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00005260    00000028     SysTick.o (.text.SysTick_Increasment)
                  00005288    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000052b0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000052d6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  000052fc    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00005322    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00005348    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000536c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00005390    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000053b4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000053d6    00000002     --HOLE-- [fill = 0]
                  000053d8    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000053f8    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00005418    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  00005438    00000020     SysTick.o (.text.Delay)
                  00005458    00000020     main.o (.text.main)
                  00005478    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005496    00000002     --HOLE-- [fill = 0]
                  00005498    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000054b6    00000002     --HOLE-- [fill = 0]
                  000054b8    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  000054d4    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  000054f0    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  0000550c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00005528    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00005544    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00005560    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000557c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00005598    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000055b4    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000055d0    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  000055ec    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00005608    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005624    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005640    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000565c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00005678    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00005694    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000056ac    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000056c4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000056dc    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000056f4    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  0000570c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00005724    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000573c    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00005754    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  0000576c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005784    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  0000579c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000057b4    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000057cc    00000018     Motor.o (.text.DL_GPIO_setPins)
                  000057e4    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  000057fc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005814    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000582c    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00005844    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  0000585c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00005874    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  0000588c    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000058a4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  000058bc    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  000058d4    00000018     MPU6050.o (.text.DL_I2C_reset)
                  000058ec    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00005904    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  0000591c    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00005934    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  0000594c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00005964    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0000597c    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00005994    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000059ac    00000018     Motor.o (.text.DL_Timer_startCounter)
                  000059c4    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  000059dc    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  000059f4    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00005a0c    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00005a24    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005a3c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005a54    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00005a6c    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00005a84    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00005a9c    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00005ab4    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  00005aca    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00005ae0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00005af6    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00005b0c    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00005b22    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00005b38    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005b4e    00000016     SysTick.o (.text.SysGetTick)
                  00005b64    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00005b7a    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00005b8e    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00005ba2    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  00005bb6    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005bca    00000002     --HOLE-- [fill = 0]
                  00005bcc    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00005be0    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00005bf4    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00005c08    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00005c1c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00005c30    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00005c44    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00005c58    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005c6c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005c7e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005c90    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005ca2    00000002     --HOLE-- [fill = 0]
                  00005ca4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005cb4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005cc4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00005cd4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005ce4    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00005cf2    00000002     --HOLE-- [fill = 0]
                  00005cf4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005d02    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005d10    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005d1e    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005d2a    00000002     --HOLE-- [fill = 0]
                  00005d2c    0000000c     SysTick.o (.text.Sys_GetTick)
                  00005d38    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005d42    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005d4c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005d5c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005d66    00000002     --HOLE-- [fill = 0]
                  00005d68    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005d78    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005d82    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005d8c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005d96    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005da0    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00005db0    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005db8    00000008     Interrupt.o (.text.SysTick_Handler)
                  00005dc0    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005dc8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005dd0    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005dd6    00000002     --HOLE-- [fill = 0]
                  00005dd8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005de8    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005dee    00000006            : exit.c.obj (.text:abort)
                  00005df4    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00005df8    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005dfc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005e00    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005e10    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005e14    0000000c     --HOLE-- [fill = 0]

.cinit     0    000060b0    00000060     
                  000060b0    0000003b     (.cinit..data.load) [load image, compression = lzss]
                  000060eb    00000001     --HOLE-- [fill = 0]
                  000060ec    0000000c     (__TI_handler_table)
                  000060f8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00006100    00000010     (__TI_cinit_table)

.rodata    0    00005e20    00000290     
                  00005e20    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005f21    00000007     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00005f28    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00005f68    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00005f90    00000028     inv_mpu.o (.rodata.test)
                  00005fb8    00000021     Task_App.o (.rodata.str1.14074990341397557290.1)
                  00005fd9    0000001f     Task_App.o (.rodata.str1.11952760121962574671.1)
                  00005ff8    0000001e     inv_mpu.o (.rodata.reg)
                  00006016    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00006018    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00006030    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00006048    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00006059    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  0000606a    0000000c     inv_mpu.o (.rodata.hw)
                  00006076    0000000b     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00006081    00000001     --HOLE-- [fill = 0]
                  00006082    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  0000608c    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00006094    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  0000609c    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  000060a4    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  000060a7    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000060a9    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000060ab    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003d3     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:ExISR_Flag)
                  202003cc    00000004     (.common:sensor_timestamp)
                  202003d0    00000002     (.common:sensors)
                  202003d2    00000001     (.common:more)

.data      0    202003d4    0000010e     UNINITIALIZED
                  202003d4    00000048     Motor.o (.data.Motor_Left)
                  2020041c    00000048     Motor.o (.data.Motor_Right)
                  20200464    0000002c     inv_mpu.o (.data.st)
                  20200490    00000010     Task_App.o (.data.Gray_Anolog)
                  202004a0    00000010     Task_App.o (.data.Gray_Normal)
                  202004b0    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004c0    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004c8    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004cc    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004d0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004d4    00000004     SysTick.o (.data.delayTick)
                  202004d8    00000004     SysTick.o (.data.uwTick)
                  202004dc    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004de    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004df    00000001     Task_App.o (.data.Gray_Digtal)
                  202004e0    00000001     Task.o (.data.Task_Num)
                  202004e1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3510    123       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3550    315       0      
                                                                 
    .\APP\Src\
       Task_App.o                       700     90        227    
       Interrupt.o                      470     0         6      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1170    90        233    
                                                                 
    .\BSP\Src\
       MPU6050.o                        1880    0         47     
       No_Mcu_Ganv_Grayscale_Sensor.o   1202    0         0      
       Serial.o                         424     0         512    
       Task.o                           674     0         241    
       Motor.o                          580     0         144    
       ADC.o                            204     0         0      
       SysTick.o                        106     0         8      
       PID_IQMath.o                     110     0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           5180    0         952    
                                                                 
    .\DMP\
       inv_mpu.o                        820     82        44     
       inv_mpu_dmp_motion_driver.o      640     0         16     
    +--+--------------------------------+-------+---------+---------+
       Total:                           1460    82        60     
                                                                 
    C:/TI/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        122     0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1208    0         0      
                                                                 
    C:/TI/mspm0_sdk_2_04_00_06/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           48      0         0      
                                                                 
    E:\TI\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8246    355       4      
                                                                 
    E:\TI\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    E:\TI\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           2980    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       95        0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     23846   937       1761   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00006100 records: 2, size/record: 8, table size: 16
	.data: load addr=000060b0, load size=0000003b bytes, run addr=202003d4, run size=0000010e bytes, compression=lzss
	.bss: load addr=000060f8, load size=00000008 bytes, run addr=20200000, run size=000003d3 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000060ec records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001ec5     00005d4c     00005d4a   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00002fb1     00005d68     00005d64   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005d80          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005d94          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005db6          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00005dec          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00002ac5     00005da0     00005d9e   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00001ecf     00005dd8     00005dd4   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005dfa          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005289     00005e00     00005dfc   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00003d3d  ADC0_IRQHandler                      
00003d3d  ADC1_IRQHandler                      
00003d3d  AES_IRQHandler                       
00005df4  C$$EXIT                              
00003d3d  CANFD0_IRQHandler                    
00003d3d  DAC0_IRQHandler                      
000049e9  DL_ADC12_setClockConfig              
00005d39  DL_Common_delayCycles                
000046d1  DL_DMA_initChannel                   
00004211  DL_I2C_fillControllerTXFIFO          
00004c61  DL_I2C_flushControllerTXFIFO         
00005323  DL_I2C_setClockConfig                
00003095  DL_SYSCTL_configSYSPLL               
00003f61  DL_SYSCTL_setHFCLKSourceHFXTParams   
000048d9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002cd9  DL_Timer_initFourCCPWMMode           
00005641  DL_Timer_setCaptCompUpdateMethod     
00005995  DL_Timer_setCaptureCompareOutCtl     
00005cb5  DL_Timer_setCaptureCompareValue      
0000565d  DL_Timer_setClockConfig              
00004801  DL_UART_init                         
00005c6d  DL_UART_setClockConfig               
00005419  DL_UART_transmitDataBlocking         
00003d3d  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004c8  Data_MotorEncoder                    
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004c0  Data_Tracker_Input                   
202004cc  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
00003d3d  Default_Handler                      
00005439  Delay                                
202003c8  ExISR_Flag                           
202004de  Flag_MPU6050_Ready                   
00003d3d  GROUP0_IRQHandler                    
00002ecd  GROUP1_IRQHandler                    
00003171  Get_Analog_value                     
00004cd9  Get_Anolog_Value                     
00005ce5  Get_Digtal_For_User                  
00004e3f  Get_Normalize_For_User               
202002f0  GraySensor                           
20200490  Gray_Anolog                          
202004df  Gray_Digtal                          
202004a0  Gray_Normal                          
00005df5  HOSTexit                             
00003d3d  HardFault_Handler                    
00003d3d  I2C0_IRQHandler                      
00003d3d  I2C1_IRQHandler                      
00004a29  Interrupt_Init                       
202003d4  Motor_Left                           
2020041c  Motor_Right                          
000036c5  Motor_SetDuty                        
00003db5  Motor_Start                          
00003a3d  MyPrintf                             
00003d3d  NMI_Handler                          
00002059  No_MCU_Ganv_Sensor_Init              
00004849  No_MCU_Ganv_Sensor_Init_Frist        
00004961  No_Mcu_Ganv_Sensor_Task_Without_tick 
000050f5  PID_IQ_Init                          
0000491d  PID_IQ_SetParams                     
00003d3d  PendSV_Handler                       
00003d3d  RTC_IRQHandler                       
000010ed  Read_Quad                            
00005dfd  Reset_Handler                        
00003d3d  SPI0_IRQHandler                      
00003d3d  SPI1_IRQHandler                      
00003d3d  SVC_Handler                          
00004385  SYSCFG_DL_ADC1_init                  
00004fe1  SYSCFG_DL_DMA_CH_RX_init             
00005a55  SYSCFG_DL_DMA_CH_TX_init             
00005d1f  SYSCFG_DL_DMA_init                   
00001959  SYSCFG_DL_GPIO_init                  
000043dd  SYSCFG_DL_I2C_MPU6050_init           
00003fc5  SYSCFG_DL_I2C_OLED_init              
00003899  SYSCFG_DL_Motor_PWM_init             
00004271  SYSCFG_DL_SYSCTL_init                
00005cc5  SYSCFG_DL_SYSTICK_init               
00003ac1  SYSCFG_DL_UART0_init                 
00005071  SYSCFG_DL_init                       
00003765  SYSCFG_DL_initPower                  
00004435  Serial_Init                          
20200000  Serial_RxData                        
00005b4f  SysGetTick                           
00005db9  SysTick_Handler                      
00005261  SysTick_Increasment                  
00005d2d  Sys_GetTick                          
00003d3d  TIMA0_IRQHandler                     
00003d3d  TIMA1_IRQHandler                     
00003d3d  TIMG0_IRQHandler                     
00003d3d  TIMG12_IRQHandler                    
00003d3d  TIMG6_IRQHandler                     
00003d3d  TIMG7_IRQHandler                     
00003d3d  TIMG8_IRQHandler                     
00005c7f  TI_memcpy_small                      
00005d11  TI_memset_small                      
000034c1  Task_Add                             
00004a69  Task_GraySensor                      
000040f1  Task_IdleFunction                    
00003805  Task_Init                            
00004151  Task_Serial                          
00001d15  Task_Start                           
0000287d  Task_Tracker                         
00003d3d  UART0_IRQHandler                     
00003d3d  UART1_IRQHandler                     
00003d3d  UART2_IRQHandler                     
00003d3d  UART3_IRQHandler                     
00005a6d  _IQ24div                             
00005a85  _IQ24mpy                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00006100  __TI_CINIT_Base                      
00006110  __TI_CINIT_Limit                     
00006110  __TI_CINIT_Warm                      
000060ec  __TI_Handler_Table_Base              
000060f8  __TI_Handler_Table_Limit             
00004d8d  __TI_auto_init_nobinit_nopinit       
00003c4d  __TI_decompress_lzss                 
00005c91  __TI_decompress_none                 
0000448d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00005b65  __TI_zero_init_nomemset              
00001ecf  __adddf3                             
0000332f  __addsf3                             
00005e20  __aeabi_ctype_table_                 
00005e20  __aeabi_ctype_table_C                
00003d41  __aeabi_d2f                          
000047b5  __aeabi_d2iz                         
000049a5  __aeabi_d2uiz                        
00001ecf  __aeabi_dadd                         
00004029  __aeabi_dcmpeq                       
00004065  __aeabi_dcmpge                       
00004079  __aeabi_dcmpgt                       
00004051  __aeabi_dcmple                       
0000403d  __aeabi_dcmplt                       
00002ac5  __aeabi_ddiv                         
00002fb1  __aeabi_dmul                         
00001ec5  __aeabi_dsub                         
202004d0  __aeabi_errno                        
00005dc1  __aeabi_errno_addr                   
00004ae9  __aeabi_f2d                          
00004e79  __aeabi_f2iz                         
0000332f  __aeabi_fadd                         
0000408d  __aeabi_fcmpeq                       
000040c9  __aeabi_fcmpge                       
000040dd  __aeabi_fcmpgt                       
000040b5  __aeabi_fcmple                       
000040a1  __aeabi_fcmplt                       
00003bc9  __aeabi_fdiv                         
00003925  __aeabi_fmul                         
00003325  __aeabi_fsub                         
000050c9  __aeabi_i2d                          
00004d15  __aeabi_i2f                          
0000453d  __aeabi_idiv                         
00002057  __aeabi_idiv0                        
0000453d  __aeabi_idivmod                      
0000361f  __aeabi_ldiv0                        
00005499  __aeabi_llsl                         
00005391  __aeabi_lmul                         
00005dc9  __aeabi_memcpy                       
00005dc9  __aeabi_memcpy4                      
00005dc9  __aeabi_memcpy8                      
00005cf5  __aeabi_memset                       
00005cf5  __aeabi_memset4                      
00005cf5  __aeabi_memset8                      
0000536d  __aeabi_ui2d                         
00004aa9  __aeabi_uidiv                        
00004aa9  __aeabi_uidivmod                     
00005c45  __aeabi_uldivmod                     
00005499  __ashldi3                            
ffffffff  __binit__                            
00003e91  __cmpdf2                             
00004dc9  __cmpsf2                             
00002ac5  __divdf3                             
00003bc9  __divsf3                             
00003e91  __eqdf2                              
00004dc9  __eqsf2                              
00004ae9  __extendsfdf2                        
000047b5  __fixdfsi                            
00004e79  __fixsfsi                            
000049a5  __fixunsdfsi                         
000050c9  __floatsidf                          
00004d15  __floatsisf                          
0000536d  __floatunsidf                        
00003cc9  __gedf2                              
00004d51  __gesf2                              
00003cc9  __gtdf2                              
00004d51  __gtsf2                              
00003e91  __ledf2                              
00004dc9  __lesf2                              
00003e91  __ltdf2                              
00004dc9  __ltsf2                              
UNDEFED   __mpu_init                           
00002fb1  __muldf3                             
00005391  __muldi3                             
00004e05  __muldsi3                            
00003925  __mulsf3                             
00003e91  __nedf2                              
00004dc9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001ec5  __subdf3                             
00003325  __subsf3                             
00003d41  __truncdfsf2                         
00003621  __udivmoddi4                         
00005289  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00005e11  _system_pre_init                     
00005def  abort                                
00004eb1  adc_getValue                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
000021e1  atan2                                
000021e1  atan2l                               
00000df5  atanl                                
00004b29  atoi                                 
ffffffff  binit                                
00003e25  convertAnalogToDigital               
202004d4  delayTick                            
00001765  dmp_read_fifo                        
202004e1  enable_group1_irq                    
000042cd  frexp                                
000042cd  frexpl                               
0000606a  hw                                   
00000000  interruptVectors                     
0000324d  ldexp                                
0000324d  ldexpl                               
00005459  main                                 
000053b5  memccpy                              
202003d2  more                                 
000041b1  mpu6050_i2c_sda_unlock               
00002bd1  mpu_read_fifo_stream                 
00001319  mpu_reset_fifo                       
00002615  mspm0_i2c_read                       
000033fd  mspm0_i2c_write                      
00003575  normalizeAnalogValues                
00002749  qsort                                
202003a0  quat                                 
00005ff8  reg                                  
0000324d  scalbn                               
0000324d  scalbnl                              
202003cc  sensor_timestamp                     
202003d0  sensors                              
00002369  sqrt                                 
00002369  sqrtl                                
00005f90  test                                 
202004d8  uwTick                               
00004b69  vsnprintf                            
00005cd5  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  Read_Quad                            
00001319  mpu_reset_fifo                       
00001765  dmp_read_fifo                        
00001959  SYSCFG_DL_GPIO_init                  
00001d15  Task_Start                           
00001ec5  __aeabi_dsub                         
00001ec5  __subdf3                             
00001ecf  __adddf3                             
00001ecf  __aeabi_dadd                         
00002057  __aeabi_idiv0                        
00002059  No_MCU_Ganv_Sensor_Init              
000021e1  atan2                                
000021e1  atan2l                               
00002369  sqrt                                 
00002369  sqrtl                                
00002615  mspm0_i2c_read                       
00002749  qsort                                
0000287d  Task_Tracker                         
00002ac5  __aeabi_ddiv                         
00002ac5  __divdf3                             
00002bd1  mpu_read_fifo_stream                 
00002cd9  DL_Timer_initFourCCPWMMode           
00002ecd  GROUP1_IRQHandler                    
00002fb1  __aeabi_dmul                         
00002fb1  __muldf3                             
00003095  DL_SYSCTL_configSYSPLL               
00003171  Get_Analog_value                     
0000324d  ldexp                                
0000324d  ldexpl                               
0000324d  scalbn                               
0000324d  scalbnl                              
00003325  __aeabi_fsub                         
00003325  __subsf3                             
0000332f  __addsf3                             
0000332f  __aeabi_fadd                         
000033fd  mspm0_i2c_write                      
000034c1  Task_Add                             
00003575  normalizeAnalogValues                
0000361f  __aeabi_ldiv0                        
00003621  __udivmoddi4                         
000036c5  Motor_SetDuty                        
00003765  SYSCFG_DL_initPower                  
00003805  Task_Init                            
00003899  SYSCFG_DL_Motor_PWM_init             
00003925  __aeabi_fmul                         
00003925  __mulsf3                             
00003a3d  MyPrintf                             
00003ac1  SYSCFG_DL_UART0_init                 
00003bc9  __aeabi_fdiv                         
00003bc9  __divsf3                             
00003c4d  __TI_decompress_lzss                 
00003cc9  __gedf2                              
00003cc9  __gtdf2                              
00003d3d  ADC0_IRQHandler                      
00003d3d  ADC1_IRQHandler                      
00003d3d  AES_IRQHandler                       
00003d3d  CANFD0_IRQHandler                    
00003d3d  DAC0_IRQHandler                      
00003d3d  DMA_IRQHandler                       
00003d3d  Default_Handler                      
00003d3d  GROUP0_IRQHandler                    
00003d3d  HardFault_Handler                    
00003d3d  I2C0_IRQHandler                      
00003d3d  I2C1_IRQHandler                      
00003d3d  NMI_Handler                          
00003d3d  PendSV_Handler                       
00003d3d  RTC_IRQHandler                       
00003d3d  SPI0_IRQHandler                      
00003d3d  SPI1_IRQHandler                      
00003d3d  SVC_Handler                          
00003d3d  TIMA0_IRQHandler                     
00003d3d  TIMA1_IRQHandler                     
00003d3d  TIMG0_IRQHandler                     
00003d3d  TIMG12_IRQHandler                    
00003d3d  TIMG6_IRQHandler                     
00003d3d  TIMG7_IRQHandler                     
00003d3d  TIMG8_IRQHandler                     
00003d3d  UART0_IRQHandler                     
00003d3d  UART1_IRQHandler                     
00003d3d  UART2_IRQHandler                     
00003d3d  UART3_IRQHandler                     
00003d41  __aeabi_d2f                          
00003d41  __truncdfsf2                         
00003db5  Motor_Start                          
00003e25  convertAnalogToDigital               
00003e91  __cmpdf2                             
00003e91  __eqdf2                              
00003e91  __ledf2                              
00003e91  __ltdf2                              
00003e91  __nedf2                              
00003f61  DL_SYSCTL_setHFCLKSourceHFXTParams   
00003fc5  SYSCFG_DL_I2C_OLED_init              
00004029  __aeabi_dcmpeq                       
0000403d  __aeabi_dcmplt                       
00004051  __aeabi_dcmple                       
00004065  __aeabi_dcmpge                       
00004079  __aeabi_dcmpgt                       
0000408d  __aeabi_fcmpeq                       
000040a1  __aeabi_fcmplt                       
000040b5  __aeabi_fcmple                       
000040c9  __aeabi_fcmpge                       
000040dd  __aeabi_fcmpgt                       
000040f1  Task_IdleFunction                    
00004151  Task_Serial                          
000041b1  mpu6050_i2c_sda_unlock               
00004211  DL_I2C_fillControllerTXFIFO          
00004271  SYSCFG_DL_SYSCTL_init                
000042cd  frexp                                
000042cd  frexpl                               
00004385  SYSCFG_DL_ADC1_init                  
000043dd  SYSCFG_DL_I2C_MPU6050_init           
00004435  Serial_Init                          
0000448d  __TI_ltoa                            
0000453d  __aeabi_idiv                         
0000453d  __aeabi_idivmod                      
000046d1  DL_DMA_initChannel                   
000047b5  __aeabi_d2iz                         
000047b5  __fixdfsi                            
00004801  DL_UART_init                         
00004849  No_MCU_Ganv_Sensor_Init_Frist        
000048d9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000491d  PID_IQ_SetParams                     
00004961  No_Mcu_Ganv_Sensor_Task_Without_tick 
000049a5  __aeabi_d2uiz                        
000049a5  __fixunsdfsi                         
000049e9  DL_ADC12_setClockConfig              
00004a29  Interrupt_Init                       
00004a69  Task_GraySensor                      
00004aa9  __aeabi_uidiv                        
00004aa9  __aeabi_uidivmod                     
00004ae9  __aeabi_f2d                          
00004ae9  __extendsfdf2                        
00004b29  atoi                                 
00004b69  vsnprintf                            
00004c61  DL_I2C_flushControllerTXFIFO         
00004cd9  Get_Anolog_Value                     
00004d15  __aeabi_i2f                          
00004d15  __floatsisf                          
00004d51  __gesf2                              
00004d51  __gtsf2                              
00004d8d  __TI_auto_init_nobinit_nopinit       
00004dc9  __cmpsf2                             
00004dc9  __eqsf2                              
00004dc9  __lesf2                              
00004dc9  __ltsf2                              
00004dc9  __nesf2                              
00004e05  __muldsi3                            
00004e3f  Get_Normalize_For_User               
00004e79  __aeabi_f2iz                         
00004e79  __fixsfsi                            
00004eb1  adc_getValue                         
00004fe1  SYSCFG_DL_DMA_CH_RX_init             
00005071  SYSCFG_DL_init                       
000050c9  __aeabi_i2d                          
000050c9  __floatsidf                          
000050f5  PID_IQ_Init                          
00005261  SysTick_Increasment                  
00005289  _c_int00_noargs                      
00005323  DL_I2C_setClockConfig                
0000536d  __aeabi_ui2d                         
0000536d  __floatunsidf                        
00005391  __aeabi_lmul                         
00005391  __muldi3                             
000053b5  memccpy                              
00005419  DL_UART_transmitDataBlocking         
00005439  Delay                                
00005459  main                                 
00005499  __aeabi_llsl                         
00005499  __ashldi3                            
00005641  DL_Timer_setCaptCompUpdateMethod     
0000565d  DL_Timer_setClockConfig              
00005995  DL_Timer_setCaptureCompareOutCtl     
00005a55  SYSCFG_DL_DMA_CH_TX_init             
00005a6d  _IQ24div                             
00005a85  _IQ24mpy                             
00005b4f  SysGetTick                           
00005b65  __TI_zero_init_nomemset              
00005c45  __aeabi_uldivmod                     
00005c6d  DL_UART_setClockConfig               
00005c7f  TI_memcpy_small                      
00005c91  __TI_decompress_none                 
00005cb5  DL_Timer_setCaptureCompareValue      
00005cc5  SYSCFG_DL_SYSTICK_init               
00005cd5  wcslen                               
00005ce5  Get_Digtal_For_User                  
00005cf5  __aeabi_memset                       
00005cf5  __aeabi_memset4                      
00005cf5  __aeabi_memset8                      
00005d11  TI_memset_small                      
00005d1f  SYSCFG_DL_DMA_init                   
00005d2d  Sys_GetTick                          
00005d39  DL_Common_delayCycles                
00005db9  SysTick_Handler                      
00005dc1  __aeabi_errno_addr                   
00005dc9  __aeabi_memcpy                       
00005dc9  __aeabi_memcpy4                      
00005dc9  __aeabi_memcpy8                      
00005def  abort                                
00005df4  C$$EXIT                              
00005df5  HOSTexit                             
00005dfd  Reset_Handler                        
00005e11  _system_pre_init                     
00005e20  __aeabi_ctype_table_                 
00005e20  __aeabi_ctype_table_C                
00005f90  test                                 
00005ff8  reg                                  
0000606a  hw                                   
000060ec  __TI_Handler_Table_Base              
000060f8  __TI_Handler_Table_Limit             
00006100  __TI_CINIT_Base                      
00006110  __TI_CINIT_Limit                     
00006110  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  ExISR_Flag                           
202003cc  sensor_timestamp                     
202003d0  sensors                              
202003d2  more                                 
202003d4  Motor_Left                           
2020041c  Motor_Right                          
20200490  Gray_Anolog                          
202004a0  Gray_Normal                          
202004c0  Data_Tracker_Input                   
202004c8  Data_MotorEncoder                    
202004cc  Data_Tracker_Offset                  
202004d0  __aeabi_errno                        
202004d4  delayTick                            
202004d8  uwTick                               
202004de  Flag_MPU6050_Ready                   
202004df  Gray_Digtal                          
202004e1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[267 symbols]
